/**
 * GENERAL PRACTITIONER AGENT
 * 
 * Implements a comprehensive GP agent that provides primary care consultations.
 * Replaces the hardcoded GP prompt with a sophisticated agent that can:
 * - Assess general health concerns
 * - Provide primary care guidance
 * - Detect when specialist referral is needed
 * - Handle routine medical questions
 * - Manage chronic conditions
 * 
 * CAPABILITIES:
 * - Primary care assessment
 * - Preventive care guidance
 * - Chronic disease management
 * - Patient education
 * - Specialist referral decisions
 */

import { BaseAgent } from './BaseAgent';
import { RAGTool } from '../tools/RAGTool';
import type {
  AgentRequest,
  AgentResponse,
  AgentRole,
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class GeneralPractitionerAgent extends BaseAgent {
  private ragTool: RAGTool;

  constructor(memoryManager: MemoryManager) {
    const id = 'gp-agent-001';
    const name = 'Dr. <PERSON>';
    const role: AgentRole = 'general_practitioner';
    const capabilities: AgentCapability[] = [
      'primary_care',
      'diagnostic_assessment',
      'treatment_planning',
      'patient_education',
      'preventive_care',
      'chronic_disease_management',
      'medication_management'
    ];

    // Initialize with RAG tool for knowledge retrieval
    const tools = [new RAGTool()];

    const systemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

AVAILABLE TOOLS:
- Medical Knowledge Retrieval (RAG): Access to current medical guidelines, research, and protocols

When providing consultations:
1. Use the RAG tool to retrieve relevant medical guidelines and research
2. Reference current evidence-based recommendations
3. Cite specific studies or guidelines when appropriate
4. Ensure recommendations align with latest clinical protocols

CORE RESPONSIBILITIES:
- Conduct thorough primary care assessments
- Provide evidence-based medical guidance
- Educate patients about their health conditions
- Identify when specialist referral is appropriate
- Manage chronic conditions and preventive care
- Ensure patient safety and appropriate care escalation

COMMUNICATION STYLE:
- Professional yet warm and empathetic
- Use clear, patient-friendly language
- Ask relevant follow-up questions for proper assessment
- Provide structured, actionable advice
- Always emphasize the importance of in-person medical care when appropriate

SAFETY PROTOCOLS:
- Immediately flag emergency situations requiring urgent care
- Recommend specialist consultation for complex conditions
- Never provide specific medication dosages without proper evaluation
- Always advise patients to seek immediate medical attention for serious symptoms
- Maintain clear boundaries of telemedicine limitations

SPECIALIZATION AREAS:
- General health assessments and wellness checks
- Common acute illnesses (colds, flu, minor infections)
- Chronic disease management (diabetes, hypertension, asthma)
- Preventive care and health screenings
- Mental health screening and basic counseling
- Women's health and family planning
- Geriatric care considerations
- Pediatric primary care (basic)

Remember: You are providing guidance and education, not replacing in-person medical examination and treatment. Always encourage patients to establish care with a local healthcare provider for ongoing medical needs.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager, tools);

    // Store RAG tool reference for easy access
    this.ragTool = tools[0] as RAGTool;
  }

  /**
   * Handle incoming patient messages
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🩺 GP Agent processing request for session: ${request.sessionId}`);

      // Detect emergencies first
      const emergencyFlags = this.detectEmergencies(request.userMessage);
      
      // If critical emergency detected, prioritize emergency response
      if (emergencyFlags.some(flag => flag.severity === 'critical')) {
        return this.handleEmergencyResponse(request, emergencyFlags);
      }

      // Analyze the request for specialist referral needs
      const handoffSuggestions = this.analyzeForSpecialistReferral(request);

      // Generate GP response using the AI orchestrator
      const response = await this.generateMedicalResponse(request);

      // Calculate confidence based on request complexity
      const confidence = this.calculateConfidence(request);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, confidence, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence,
        reasoning: 'Primary care assessment completed with evidence-based guidance',
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: this.generateFollowUpActions(request),
        metadata: {
          responseTime,
          assessmentType: 'primary_care',
          specialistConsultationRecommended: handoffSuggestions.length > 0
        }
      };

    } catch (error) {
      console.error('❌ GP Agent error:', error);
      
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I apologize, but I'm experiencing some technical difficulties. For your safety, please consult with a healthcare provider directly if you have urgent medical concerns.",
        confidence: 0.1,
        reasoning: 'Technical error occurred during consultation',
        emergencyFlags: [{
          type: 'medical_emergency',
          severity: 'medium',
          description: 'System error - recommend direct medical consultation',
          recommendedAction: 'Consult healthcare provider directly',
          timeToResponse: 1500
        }]
      };
    }
  }

  /**
   * Handle emergency situations
   */
  private async handleEmergencyResponse(request: AgentRequest, emergencyFlags: EmergencyFlag[]): Promise<AgentResponse> {
    console.log('🚨 GP Agent handling emergency situation');

    const emergencyResponse = `I've detected that you may be experiencing a medical emergency. This requires immediate attention from emergency medical services.

IMMEDIATE ACTIONS:
1. If this is life-threatening, call emergency services (911/999/112) immediately
2. If you're experiencing chest pain, difficulty breathing, or severe symptoms, seek emergency care now
3. Contact your local emergency department or urgent care facility

I'm designed to provide general health guidance, but emergency situations require immediate professional medical intervention. Please don't delay in seeking appropriate emergency care.

If this is not an emergency, please rephrase your concern and I'll be happy to provide general health guidance.`;

    return {
      agentId: this.id,
      agentName: this.name,
      content: emergencyResponse,
      confidence: 0.95,
      reasoning: 'Emergency situation detected - immediate medical attention required',
      emergencyFlags,
      suggestedHandoffs: [{
        targetAgentRole: 'emergency',
        reason: 'Critical emergency situation detected',
        urgency: 'critical',
        contextToTransfer: `Emergency detected: ${emergencyFlags.map(f => f.description).join(', ')}`,
        patientConsent: false // Emergency override
      }],
      metadata: {
        emergencyDetected: true,
        responseTime: Date.now(),
        priorityLevel: 'critical'
      }
    };
  }

  /**
   * Analyze if specialist referral is needed
   */
  private analyzeForSpecialistReferral(request: AgentRequest): AgentHandoffSuggestion[] {
    const message = request.userMessage.toLowerCase();
    const suggestions: AgentHandoffSuggestion[] = [];

    // Cardiology referral indicators
    const cardioKeywords = ['heart', 'chest pain', 'palpitations', 'blood pressure', 'cardiac', 'cardiovascular'];
    if (cardioKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'cardiologist',
        reason: 'Cardiovascular symptoms detected - specialist consultation recommended',
        urgency: 'medium',
        contextToTransfer: 'Patient presenting with cardiovascular-related concerns'
      });
    }

    // Mental health referral indicators
    const mentalHealthKeywords = ['depression', 'anxiety', 'stress', 'mental health', 'mood', 'panic', 'therapy'];
    if (mentalHealthKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'psychiatrist',
        reason: 'Mental health concerns identified - specialist support recommended',
        urgency: 'medium',
        contextToTransfer: 'Patient expressing mental health concerns'
      });
    }

    // Nutrition referral indicators
    const nutritionKeywords = ['diet', 'weight', 'nutrition', 'eating', 'food', 'diabetes', 'cholesterol'];
    if (nutritionKeywords.some(keyword => message.includes(keyword))) {
      suggestions.push({
        targetAgentRole: 'nutritionist',
        reason: 'Nutritional concerns identified - dietary specialist consultation beneficial',
        urgency: 'low',
        contextToTransfer: 'Patient has nutrition-related questions or concerns'
      });
    }

    return suggestions;
  }

  /**
   * Generate medical response using AI orchestrator
   */
  private async generateMedicalResponse(request: AgentRequest): Promise<string> {
    // TODO: Integrate with AI orchestrator for dynamic responses
    // For now, return a structured response based on the request
    
    const hasSymptoms = request.userMessage.toLowerCase().includes('symptom') || 
                       request.userMessage.toLowerCase().includes('pain') ||
                       request.userMessage.toLowerCase().includes('feel');

    if (hasSymptoms) {
      return `Thank you for sharing your concerns with me. As your primary care provider, I want to help you understand your symptoms and guide you toward appropriate care.

Based on what you've described, I'd like to gather a bit more information to provide you with the best guidance:

1. How long have you been experiencing these symptoms?
2. On a scale of 1-10, how would you rate the severity?
3. Have you noticed any patterns or triggers?
4. Are you currently taking any medications?

While I can provide general guidance and education, it's important that you have a proper medical evaluation with a healthcare provider who can perform a physical examination and review your complete medical history.

In the meantime, please monitor your symptoms and seek immediate medical attention if they worsen or if you develop any concerning new symptoms.

Is there anything specific about your symptoms you'd like me to explain or any general health questions I can help address?`;
    }

    return `Hello! I'm Dr. Sarah Chen, and I'm here to provide you with general health guidance and education. 

I can help you with:
- Understanding common health conditions
- General wellness and preventive care advice
- Guidance on when to seek medical attention
- Basic health education and lifestyle recommendations

Please remember that while I can provide general information and guidance, this doesn't replace a proper medical examination with a healthcare provider. For specific medical concerns, diagnosis, or treatment, you should consult with a licensed healthcare professional in your area.

How can I assist you with your health questions today?`;
  }

  /**
   * Calculate confidence score for the response
   */
  private calculateConfidence(request: AgentRequest): number {
    let confidence = 0.8; // Base confidence for GP

    // Reduce confidence for complex specialist topics
    const specialistKeywords = ['surgery', 'oncology', 'neurology', 'complex cardiac'];
    if (specialistKeywords.some(keyword => 
      request.userMessage.toLowerCase().includes(keyword)
    )) {
      confidence -= 0.3;
    }

    // Increase confidence for common primary care topics
    const primaryCareKeywords = ['cold', 'flu', 'general health', 'wellness', 'prevention'];
    if (primaryCareKeywords.some(keyword => 
      request.userMessage.toLowerCase().includes(keyword)
    )) {
      confidence += 0.1;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Generate follow-up actions
   */
  private generateFollowUpActions(request: AgentRequest) {
    const actions = [];

    // Always recommend establishing care with a local provider
    actions.push({
      type: 'schedule_appointment' as const,
      description: 'Schedule appointment with local primary care provider',
      timeframe: 'Within 2-4 weeks for routine care',
      priority: 'medium' as const
    });

    // Add specific follow-ups based on content
    if (request.userMessage.toLowerCase().includes('medication')) {
      actions.push({
        type: 'medication_reminder' as const,
        description: 'Review current medications with healthcare provider',
        timeframe: 'Next appointment',
        priority: 'high' as const
      });
    }

    return actions;
  }

  /**
   * Enhanced confidence scoring for GP requests
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = super.getConfidenceScore(request);

    // GP is good for general health questions
    const generalHealthKeywords = [
      'general health', 'wellness', 'prevention', 'routine', 'checkup',
      'primary care', 'family medicine', 'common cold', 'flu'
    ];

    const message = request.userMessage.toLowerCase();
    if (generalHealthKeywords.some(keyword => message.includes(keyword))) {
      score += 0.2;
    }

    // Reduce score for highly specialized topics
    const specializedKeywords = [
      'surgery', 'oncology', 'neurosurgery', 'complex cardiac procedures'
    ];

    if (specializedKeywords.some(keyword => message.includes(keyword))) {
      score -= 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }
}

export default GeneralPractitionerAgent;
