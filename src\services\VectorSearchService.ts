/**
 * VECTOR SEARCH SERVICE FOR VOICEHEALTH AI
 * 
 * Handles semantic search using Supabase vector database with pgvector extension.
 * Provides embedding generation and similarity search for medical documents.
 * 
 * FEATURES:
 * - Semantic search with vector embeddings
 * - Multiple embedding model support (OpenAI, BioBERT, ClinicalBERT)
 * - HIPAA-compliant document access logging
 * - Performance optimization with caching
 * - Fallback mechanisms for development
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

export interface VectorSearchQuery {
  query: string;
  maxResults?: number;
  minRelevanceScore?: number;
  documentTypes?: string[];
  specialtyFilter?: string;
  evidenceLevels?: string[];
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface VectorSearchResult {
  documents: RetrievedDocument[];
  totalResults: number;
  searchTime: number;
  relevanceScores: number[];
  sources: string[];
  averageRelevance: number;
}

export interface RetrievedDocument {
  id: string;
  title: string;
  content: string;
  documentType: 'guideline' | 'protocol' | 'research' | 'advisory' | 'reference';
  specialty: string;
  source: string;
  sourceUrl?: string;
  evidenceLevel: 'A' | 'B' | 'C' | 'D';
  lastUpdated: string;
  relevanceScore: number;
  metadata: Record<string, any>;
}

export interface EmbeddingOptions {
  model?: 'openai' | 'biobert' | 'clinicalbert';
  maxTokens?: number;
  chunkSize?: number;
}

export class VectorSearchService {
  private supabase: SupabaseClient;
  private readonly documentsTable = 'medical_documents';
  private readonly embeddingsTable = 'document_embeddings';
  private readonly accessLogTable = 'document_access_log';
  private embeddingCache: Map<string, number[]> = new Map();

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for vector search');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    console.log('✅ VectorSearchService initialized with Supabase pgvector');
  }

  /**
   * Search documents using semantic vector similarity
   */
  async searchDocuments(
    query: VectorSearchQuery,
    agentId: string,
    sessionId: string,
    options: EmbeddingOptions = {}
  ): Promise<VectorSearchResult> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Performing vector search for: "${query.query}"`);

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query.query, options.model);

      // Perform similarity search using Supabase RPC function
      const { data: searchResults, error } = await this.supabase.rpc('match_documents', {
        query_embedding: queryEmbedding,
        match_threshold: query.minRelevanceScore || 0.7,
        match_count: query.maxResults || 5,
        document_types: query.documentTypes || null,
        specialty_filter: query.specialtyFilter || null,
        evidence_levels: query.evidenceLevels || null
      });

      if (error) {
        console.error('❌ Vector search RPC failed:', error);
        throw new Error(`Vector search failed: ${error.message}`);
      }

      // Transform results to RetrievedDocument format
      const documents: RetrievedDocument[] = searchResults.map((result: any) => ({
        id: result.document_id,
        title: result.title,
        content: result.content,
        documentType: result.document_type,
        specialty: result.specialty,
        source: result.source,
        sourceUrl: result.source_url,
        evidenceLevel: result.evidence_level,
        lastUpdated: result.last_updated,
        relevanceScore: result.similarity,
        metadata: result.metadata || {}
      }));

      const searchTime = Date.now() - startTime;
      const relevanceScores = documents.map(doc => doc.relevanceScore);
      const averageRelevance = relevanceScores.length > 0 
        ? relevanceScores.reduce((a, b) => a + b, 0) / relevanceScores.length 
        : 0;

      // Log document access for HIPAA compliance
      await this.logDocumentAccess(documents, agentId, sessionId, query.query, 'search');

      console.log(`✅ Vector search completed: ${documents.length} documents in ${searchTime}ms`);

      return {
        documents,
        totalResults: documents.length,
        searchTime,
        relevanceScores,
        sources: [...new Set(documents.map(doc => doc.source))],
        averageRelevance
      };

    } catch (error) {
      console.error('❌ Vector search failed:', error);
      throw error;
    }
  }

  /**
   * Generate embedding for text using specified model
   */
  async generateEmbedding(text: string, model: string = 'openai'): Promise<number[]> {
    try {
      // Check cache first
      const cacheKey = `${model}:${text.substring(0, 100)}`;
      if (this.embeddingCache.has(cacheKey)) {
        console.log('📋 Using cached embedding');
        return this.embeddingCache.get(cacheKey)!;
      }

      console.log(`🧠 Generating embedding using ${model} model`);

      // Try to call Supabase Edge Function for embedding generation
      const { data, error } = await this.supabase.functions.invoke('generate-embedding', {
        body: {
          text: text,
          model: model
        }
      });

      if (error) {
        console.warn('⚠️ Embedding generation failed, using fallback:', error);
        return this.generateFallbackEmbedding(text);
      }

      // Cache the embedding
      this.embeddingCache.set(cacheKey, data.embedding);
      
      // Limit cache size
      if (this.embeddingCache.size > 1000) {
        const firstKey = this.embeddingCache.keys().next().value;
        this.embeddingCache.delete(firstKey);
      }

      return data.embedding;

    } catch (error) {
      console.warn('⚠️ Failed to generate embedding, using fallback:', error);
      return this.generateFallbackEmbedding(text);
    }
  }

  /**
   * Fallback embedding generation for development/testing
   */
  private generateFallbackEmbedding(text: string): number[] {
    console.log('🔄 Using fallback embedding generation');
    
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(1536).fill(0); // OpenAI embedding dimension
    
    // Simple hash-based embedding with medical term weighting
    const medicalTerms = [
      'symptom', 'diagnosis', 'treatment', 'medication', 'patient', 'clinical',
      'medical', 'health', 'disease', 'condition', 'therapy', 'protocol'
    ];
    
    words.forEach((word, index) => {
      const hash = this.simpleHash(word);
      const weight = medicalTerms.includes(word) ? 2.0 : 1.0; // Boost medical terms
      embedding[hash % 1536] += weight / (index + 1);
    });

    // Normalize
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  /**
   * Simple hash function for fallback embedding
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Log document access for HIPAA compliance
   */
  private async logDocumentAccess(
    documents: RetrievedDocument[],
    agentId: string,
    sessionId: string,
    queryText: string,
    accessType: 'search' | 'retrieve' | 'cite'
  ): Promise<void> {
    try {
      const accessLogs = documents.map(doc => ({
        document_id: doc.id,
        agent_id: agentId,
        session_id: sessionId,
        access_type: accessType,
        query_text: queryText,
        relevance_score: doc.relevanceScore,
        accessed_at: new Date().toISOString(),
        user_context: {
          document_type: doc.documentType,
          specialty: doc.specialty,
          evidence_level: doc.evidenceLevel
        }
      }));

      const { error } = await this.supabase
        .from(this.accessLogTable)
        .insert(accessLogs);

      if (error) {
        console.error('❌ Failed to log document access:', error);
        // Don't throw - logging failure shouldn't break search
      } else {
        console.log(`📝 Logged access to ${documents.length} documents`);
      }

    } catch (error) {
      console.error('❌ Document access logging failed:', error);
    }
  }

  /**
   * Get document by ID with access logging
   */
  async getDocument(
    documentId: string,
    agentId: string,
    sessionId: string
  ): Promise<RetrievedDocument | null> {
    try {
      const { data, error } = await this.supabase
        .from(this.documentsTable)
        .select('*')
        .eq('id', documentId)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        console.error('❌ Document not found:', documentId);
        return null;
      }

      const document: RetrievedDocument = {
        id: data.id,
        title: data.title,
        content: data.content,
        documentType: data.document_type,
        specialty: data.specialty,
        source: data.source,
        sourceUrl: data.source_url,
        evidenceLevel: data.evidence_level,
        lastUpdated: data.last_updated,
        relevanceScore: 1.0, // Direct retrieval
        metadata: data.metadata || {}
      };

      // Log document access
      await this.logDocumentAccess([document], agentId, sessionId, '', 'retrieve');

      return document;

    } catch (error) {
      console.error('❌ Failed to get document:', error);
      return null;
    }
  }

  /**
   * Health check for vector search service
   */
  async healthCheck(): Promise<{ healthy: boolean; details: string }> {
    try {
      // Test RPC function availability
      const { error } = await this.supabase.rpc('match_documents', {
        query_embedding: new Array(1536).fill(0),
        match_threshold: 0.9,
        match_count: 1
      });

      if (error && !error.message.includes('no rows')) {
        return {
          healthy: false,
          details: `Vector search RPC unavailable: ${error.message}`
        };
      }

      return {
        healthy: true,
        details: 'Vector search service operational'
      };

    } catch (error) {
      return {
        healthy: false,
        details: `Vector search health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get search statistics
   */
  getSearchStatistics(): {
    cacheSize: number;
    cacheHitRate: number;
    totalSearches: number;
  } {
    return {
      cacheSize: this.embeddingCache.size,
      cacheHitRate: 0.85, // Placeholder - would track in real implementation
      totalSearches: 0 // Placeholder - would track in real implementation
    };
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.embeddingCache.clear();
    console.log('🗑️ Embedding cache cleared');
  }
}

// Export singleton instance
export const vectorSearchService = new VectorSearchService();
export default vectorSearchService;
