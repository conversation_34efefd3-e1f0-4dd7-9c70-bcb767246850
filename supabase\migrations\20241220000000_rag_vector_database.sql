-- RAG Vector Database Setup for VoiceHealth AI
-- Implements pgvector extension for semantic search of medical knowledge

-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Medical Documents Table
-- Stores curated medical knowledge sources with metadata
CREATE TABLE IF NOT EXISTS public.medical_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    document_type TEXT NOT NULL CHECK (document_type IN ('guideline', 'protocol', 'research', 'advisory', 'reference')),
    specialty TEXT NOT NULL,
    source TEXT NOT NULL,
    source_url TEXT,
    evidence_level TEXT NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    last_updated TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Document Embeddings Table
-- Stores vector embeddings for semantic search
CREATE TABLE IF NOT EXISTS public.document_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES public.medical_documents(id) ON DELETE CASCADE,
    embedding vector(1536), -- OpenAI embedding dimension
    embedding_model TEXT NOT NULL DEFAULT 'text-embedding-ada-002',
    chunk_index INTEGER DEFAULT 0, -- For large documents split into chunks
    chunk_text TEXT, -- The specific text chunk this embedding represents
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Knowledge Sources Table
-- Tracks trusted medical knowledge sources
CREATE TABLE IF NOT EXISTS public.knowledge_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    source_type TEXT NOT NULL CHECK (source_type IN ('official_guideline', 'peer_reviewed', 'clinical_trial', 'health_authority', 'medical_society')),
    authority_level INTEGER NOT NULL CHECK (authority_level BETWEEN 1 AND 5), -- 5 = highest authority
    region TEXT, -- Geographic applicability
    specialty TEXT,
    is_verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Document Access Log
-- HIPAA-compliant audit trail for knowledge access
CREATE TABLE IF NOT EXISTS public.document_access_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES public.medical_documents(id),
    agent_id TEXT NOT NULL,
    session_id UUID,
    access_type TEXT NOT NULL CHECK (access_type IN ('search', 'retrieve', 'cite')),
    query_text TEXT,
    relevance_score DECIMAL(3,2),
    accessed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    user_context JSONB DEFAULT '{}'
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_medical_documents_type ON public.medical_documents (document_type);
CREATE INDEX IF NOT EXISTS idx_medical_documents_specialty ON public.medical_documents (specialty);
CREATE INDEX IF NOT EXISTS idx_medical_documents_evidence ON public.medical_documents (evidence_level);
CREATE INDEX IF NOT EXISTS idx_medical_documents_active ON public.medical_documents (is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_medical_documents_updated ON public.medical_documents (last_updated DESC);

-- Vector similarity search index (ivfflat for cosine similarity)
CREATE INDEX IF NOT EXISTS idx_document_embeddings_vector 
ON public.document_embeddings USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

CREATE INDEX IF NOT EXISTS idx_document_embeddings_document ON public.document_embeddings (document_id);
CREATE INDEX IF NOT EXISTS idx_document_embeddings_model ON public.document_embeddings (embedding_model);

-- Access log indexes for audit queries
CREATE INDEX IF NOT EXISTS idx_document_access_agent ON public.document_access_log (agent_id);
CREATE INDEX IF NOT EXISTS idx_document_access_session ON public.document_access_log (session_id);
CREATE INDEX IF NOT EXISTS idx_document_access_time ON public.document_access_log (accessed_at DESC);

-- RLS Policies for HIPAA Compliance
ALTER TABLE public.medical_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.knowledge_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_access_log ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read active medical documents
CREATE POLICY "Allow authenticated read of active documents" ON public.medical_documents
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Policy: Allow authenticated users to read embeddings for active documents
CREATE POLICY "Allow authenticated read of embeddings" ON public.document_embeddings
    FOR SELECT USING (
        auth.role() = 'authenticated' AND 
        EXISTS (
            SELECT 1 FROM public.medical_documents 
            WHERE id = document_embeddings.document_id AND is_active = true
        )
    );

-- Policy: Allow authenticated users to read verified knowledge sources
CREATE POLICY "Allow authenticated read of verified sources" ON public.knowledge_sources
    FOR SELECT USING (auth.role() = 'authenticated' AND is_verified = true);

-- Policy: Allow authenticated users to log document access
CREATE POLICY "Allow authenticated document access logging" ON public.document_access_log
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Allow users to read their own access logs
CREATE POLICY "Allow read own access logs" ON public.document_access_log
    FOR SELECT USING (auth.role() = 'authenticated');

-- Vector Similarity Search RPC Function
CREATE OR REPLACE FUNCTION match_documents(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.7,
  match_count int DEFAULT 5,
  document_types text[] DEFAULT NULL,
  specialty_filter text DEFAULT NULL,
  evidence_levels text[] DEFAULT NULL
)
RETURNS TABLE (
  document_id uuid,
  title text,
  content text,
  document_type text,
  specialty text,
  source text,
  source_url text,
  evidence_level text,
  last_updated timestamptz,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id as document_id,
    d.title,
    d.content,
    d.document_type,
    d.specialty,
    d.source,
    d.source_url,
    d.evidence_level,
    d.last_updated,
    d.metadata,
    (1 - (e.embedding <=> query_embedding)) as similarity
  FROM public.medical_documents d
  JOIN public.document_embeddings e ON d.id = e.document_id
  WHERE 
    d.is_active = true
    AND (1 - (e.embedding <=> query_embedding)) > match_threshold
    AND (document_types IS NULL OR d.document_type = ANY(document_types))
    AND (specialty_filter IS NULL OR d.specialty ILIKE '%' || specialty_filter || '%')
    AND (evidence_levels IS NULL OR d.evidence_level = ANY(evidence_levels))
  ORDER BY e.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Embedding Generation Helper Function
CREATE OR REPLACE FUNCTION generate_document_embedding(
  document_id_param uuid,
  text_content text,
  model_name text DEFAULT 'text-embedding-ada-002'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  embedding_id uuid;
BEGIN
  -- This function would be called after generating embeddings via external API
  -- For now, it creates a placeholder entry
  INSERT INTO public.document_embeddings (document_id, embedding_model, chunk_text)
  VALUES (document_id_param, model_name, text_content)
  RETURNING id INTO embedding_id;
  
  RETURN embedding_id;
END;
$$;

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_medical_documents_updated_at 
    BEFORE UPDATE ON public.medical_documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON public.medical_documents TO authenticated;
GRANT SELECT ON public.document_embeddings TO authenticated;
GRANT SELECT ON public.knowledge_sources TO authenticated;
GRANT INSERT, SELECT ON public.document_access_log TO authenticated;
GRANT EXECUTE ON FUNCTION match_documents TO authenticated;
GRANT EXECUTE ON FUNCTION generate_document_embedding TO authenticated;
